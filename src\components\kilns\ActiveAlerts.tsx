
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ActiveAlertsDialog } from "./ActiveAlertsDialog";

interface AlertItem {
  id: string;
  kiln: string;
  chamber: string;
  parameter: string;
  value: string;
  expected: string;
  type: 'temperature' | 'pressure' | 'other';
  severity: 'high' | 'medium' | 'low';
  timestamp: string;
}

const mockAlerts: AlertItem[] = [
  {
    id: '1',
    kiln: 'Habla Kiln',
    chamber: 'Ch1',
    parameter: 'O₂',
    value: '3.1',
    expected: '3-5',
    type: 'other',
    severity: 'medium',
    timestamp: '2024-01-15 14:30'
  },
  {
    id: '2', 
    kiln: 'Habla Kiln',
    chamber: 'Ch1',
    parameter: 'CO₂',
    value: '16.4',
    expected: '12-15',
    type: 'other',
    severity: 'high',
    timestamp: '2024-01-15 14:25'
  },
  {
    id: '3',
    kiln: 'Habla Kiln',
    chamber: 'Ch1',
    parameter: 'Fire Zone Temp',
    value: '300',
    expected: '650-950',
    type: 'temperature',
    severity: 'high',
    timestamp: '2024-01-15 14:20'
  },
  {
    id: '4',
    kiln: 'Habla Kiln', 
    chamber: 'Ch1',
    parameter: 'Draught Pressure',
    value: '2.6',
    expected: '3-4',
    type: 'pressure',
    severity: 'medium',
    timestamp: '2024-01-15 14:15'
  }
];

export const ActiveAlerts: React.FC = () => {
  const [showAllAlerts, setShowAllAlerts] = useState(false);

  return (
    <>
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Active Alerts ({mockAlerts.length})
            </CardTitle>
            <Button 
              variant="outline" 
              size="sm" 
              className="text-red-600 border-red-200 hover:bg-red-50"
              onClick={() => setShowAllAlerts(true)}
            >
              <Eye className="h-4 w-4 mr-2" />
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {mockAlerts.map((alert) => (
              <Alert key={alert.id} variant="destructive" className="border-red-200 bg-red-50">
                <AlertDescription className="text-sm">
                  <span className="font-semibold text-red-800">{alert.kiln} {alert.chamber}:</span>{' '}
                  <span className="text-red-700">
                    {alert.parameter} = {alert.value} (Expected: {alert.expected})
                  </span>
                </AlertDescription>
              </Alert>
            ))}
          </div>
        </CardContent>
      </Card>

      <ActiveAlertsDialog
        open={showAllAlerts}
        onOpenChange={setShowAllAlerts}
        alerts={mockAlerts}
      />
    </>
  );
};
