
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { useChamberZones } from "@/hooks/useKilnMonitoring";

const KILN_OPTIONS = [
  { id: "habla-kiln", name: "Habla Kiln", chambers: 10 },
  { id: "kiln-1", name: "Kiln 1", chambers: 24 },
  { id: "kiln-2", name: "Kiln 2", chambers: 24 },
  { id: "kiln-3", name: "Kiln 3", chambers: 24 },
  { id: "kiln-4", name: "Kiln 4", chambers: 24 },
  { id: "kiln-5", name: "Kiln 5", chambers: 24 },
];

const ZONE_COLORS = {
  "Inactive": "#e2e8f0",
  "Preheat 1": "#93c5fd",
  "Preheat 2": "#60a5fa",
  "Pre-combustion": "#eab308",
  "Firing": "#f97316",
  "Cooling 1": "#dc2626",
  "Cooling 2": "#fca5a5",
  "Setting": "#16a34a",
  "Dehacking": "#fca5a5"
};

interface KilnZoneOverviewProps {
  showLegend?: boolean;
}

export const KilnZoneOverview = ({ showLegend = true }: KilnZoneOverviewProps) => {
  const { data: chamberZones = [] } = useChamberZones();

  const renderKilnDiagram = (kiln: any) => {
    const chambers = Array.from({ length: kiln.chambers }, (_, i) => i + 1);

    return (
      <div key={kiln.id} className="mb-6">
        <h3 className="font-semibold text-lg mb-3">{kiln.name}</h3>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 2xl:grid-cols-12 gap-2">
          {chambers.map((chamber) => {
            const zone = chamberZones.find(
              z => z.kiln_id === kiln.id && z.chamber_number === chamber
            )?.zone || "Inactive";

            const zoneColor = ZONE_COLORS[zone as keyof typeof ZONE_COLORS] || ZONE_COLORS.Inactive;

            return (
              <div
                key={chamber}
                className="border rounded p-2 text-center min-h-[60px] flex flex-col justify-center"
                style={{ backgroundColor: zoneColor }}
              >
                <div className="text-xs font-semibold mb-1">Ch {chamber}</div>
                <div className="text-xs text-gray-700 font-medium truncate">
                  {zone}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kiln Zone Overview - All Kilns</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {KILN_OPTIONS.map((kiln) => renderKilnDiagram(kiln))}
        </div>
        
        {/* Zone Color Legend - conditionally rendered */}
        {showLegend && (
          <div className="mt-6 pt-4 border-t">
            <h4 className="font-semibold mb-3">Zone Legend</h4>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:flex lg:flex-wrap gap-2 sm:gap-4">
              {Object.entries(ZONE_COLORS).map(([zone, color]) => (
                <div key={zone} className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded border"
                    style={{ backgroundColor: color }}
                  />
                  <span className="text-sm">{zone}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
