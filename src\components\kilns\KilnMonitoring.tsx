
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { 
  useEnhancedParameterNorms, 
  useCreateMeasurement,
  useChamberZones,
  useUpdateChamberZone 
} from "@/hooks/useKilnMonitoring";
import { KILN_OPTIONS } from "@/types/kilnMonitoring";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";
import { KilnZoneOverview } from "./KilnZoneOverview";
import { ParameterTrends } from "./ParameterTrends";
import { EnhancedParameterNorms } from "./EnhancedParameterNorms";
import { DailySummaryView } from "./DailySummaryView";
import { ChamberProductionTable } from "./ChamberProductionTable";
import { ActiveAlerts } from "./ActiveAlerts";

const ZONES = ['Inactive', 'Preheat 1', 'Preheat 2', 'Pre-combustion', 'Firing', 'Cooling 1', 'Cooling 2', 'Setting', 'Dehacking'];

export const KilnMonitoring = () => {
  const { currentUser } = useAuth();
  const { data: parameterNorms = [] } = useEnhancedParameterNorms();
  const { data: chamberZones = [] } = useChamberZones();
  const updateChamberZone = useUpdateChamberZone();
  const createMeasurement = useCreateMeasurement();

  const [selectedKiln, setSelectedKiln] = useState<string>('');
  const [selectedChamber, setSelectedChamber] = useState<number>(1);
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [parameters, setParameters] = useState<Record<string, number>>({});
  const [greenBrickWeight, setGreenBrickWeight] = useState<string>('');
  const [firedBrickWeight, setFiredBrickWeight] = useState<string>('');

  // Initialize time to current hour
  useEffect(() => {
    const now = new Date();
    const currentHour = now.getHours().toString().padStart(2, '0') + ':00';
    setSelectedTime(currentHour);
  }, []);

  const handleParameterChange = (paramName: string, value: string) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setParameters(prev => ({
        ...prev,
        [paramName]: numValue
      }));
    } else {
      setParameters(prev => {
        const newParams = { ...prev };
        delete newParams[paramName];
        return newParams;
      });
    }
  };

  const handleZoneChange = (kilnId: string, chamberNumber: number, zone: string) => {
    updateChamberZone.mutate({
      kiln_id: kilnId,
      chamber_number: chamberNumber,
      zone
    });
  };

  const handleSubmit = async () => {
    if (!selectedKiln || !selectedTime) {
      toast.error('Please select kiln and time');
      return;
    }

    if (Object.keys(parameters).length === 0 && !greenBrickWeight && !firedBrickWeight) {
      toast.error('Please enter at least one measurement');
      return;
    }

    try {
      const measurementData = {
        kiln_id: selectedKiln,
        chamber_number: selectedChamber,
        fire_zone: 'active', // Add required fire_zone field
        measurement_time: selectedTime,
        measurement_date: new Date().toISOString().split('T')[0],
        parameters,
        green_brick_weight: greenBrickWeight ? parseFloat(greenBrickWeight) : undefined,
        fired_brick_weight: firedBrickWeight ? parseFloat(firedBrickWeight) : undefined,
        user_id: currentUser?.id
      };

      await createMeasurement.mutateAsync(measurementData);
      
      // Reset form
      setParameters({});
      setGreenBrickWeight('');
      setFiredBrickWeight('');
      toast.success('Measurement recorded successfully');
    } catch (error) {
      console.error('Error submitting measurement:', error);
      toast.error('Failed to record measurement');
    }
  };

  const getZoneForChamber = (kilnId: string, chamberNumber: number) => {
    const zone = chamberZones.find(z => z.kiln_id === kilnId && z.chamber_number === chamberNumber);
    return zone?.zone || 'Inactive';
  };

  const selectedKilnConfig = KILN_OPTIONS.find(k => k.id === selectedKiln);

  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="data-entry">Data Entry</TabsTrigger>
          <TabsTrigger value="zone-control">Zone Control</TabsTrigger>
          <TabsTrigger value="parameter-norms">Parameter Norms</TabsTrigger>
          <TabsTrigger value="daily-summary">Daily Summary</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="data-entry" className="mt-6">
          {/* Data Entry Tab Content */}
          <Card>
            <CardHeader>
              <CardTitle>Data Entry</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="kiln">Kiln</Label>
                  <Select value={selectedKiln} onValueChange={setSelectedKiln}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select kiln" />
                    </SelectTrigger>
                    <SelectContent>
                      {KILN_OPTIONS.map(kiln => (
                        <SelectItem key={kiln.id} value={kiln.id}>
                          {kiln.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="chamber">Chamber</Label>
                  <Select 
                    value={selectedChamber.toString()} 
                    onValueChange={(value) => setSelectedChamber(parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {selectedKilnConfig && Array.from({ length: selectedKilnConfig.chambers }, (_, i) => i + 1).map(chamber => (
                        <SelectItem key={chamber} value={chamber.toString()}>
                          Chamber {chamber}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="time">Time</Label>
                  <Input
                    id="time"
                    type="time"
                    value={selectedTime}
                    onChange={(e) => setSelectedTime(e.target.value)}
                  />
                </div>
              </div>

              <Separator />

              {/* Parameter Inputs - Ordered as specified */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Temperature Parameters */}
                {['Pre-heat Zone 1 Temp', 'Pre-heat Zone 2 Temp', 'Pre-combustion Zone Temp', 'Fire Zone Temp', 'Cooling Zone 1 Temp', 'Cooling Zone 2 Temp'].map(paramName => {
                  const norm = parameterNorms.find(n => n.parameter_name === paramName);
                  if (!norm) return null;
                  return (
                    <div key={norm.id}>
                      <Label htmlFor={norm.parameter_name}>
                        {norm.parameter_name} ({norm.unit})
                      </Label>
                      <Input
                        id={norm.parameter_name}
                        type="number"
                        step="0.01"
                        value={parameters[norm.parameter_name] || ''}
                        onChange={(e) => handleParameterChange(norm.parameter_name, e.target.value)}
                        placeholder={`${norm.min_value} - ${norm.max_value}`}
                      />
                      <div className="text-xs text-slate-500 mt-1">
                        Normal range: {norm.min_value} - {norm.max_value} {norm.unit}
                      </div>
                    </div>
                  );
                })}

                {/* Operational Parameters */}
                {['Fire Position in Chamber', 'Fire Movement'].map(paramName => {
                  const norm = parameterNorms.find(n => n.parameter_name === paramName);
                  if (!norm) return null;
                  return (
                    <div key={norm.id}>
                      <Label htmlFor={norm.parameter_name}>
                        {norm.parameter_name} ({norm.unit})
                      </Label>
                      <Input
                        id={norm.parameter_name}
                        type="number"
                        step="0.01"
                        value={parameters[norm.parameter_name] || ''}
                        onChange={(e) => handleParameterChange(norm.parameter_name, e.target.value)}
                        placeholder={`${norm.min_value} - ${norm.max_value}`}
                      />
                      <div className="text-xs text-slate-500 mt-1">
                        Normal range: {norm.min_value} - {norm.max_value} {norm.unit}
                      </div>
                    </div>
                  );
                })}

                {/* Combustion Parameters */}
                {['O2', 'CO2', 'Draught Pressure'].map(paramName => {
                  const norm = parameterNorms.find(n => n.parameter_name === paramName);
                  if (!norm) return null;
                  return (
                    <div key={norm.id}>
                      <Label htmlFor={norm.parameter_name}>
                        {norm.parameter_name} ({norm.unit})
                      </Label>
                      <Input
                        id={norm.parameter_name}
                        type="number"
                        step="0.01"
                        value={parameters[norm.parameter_name] || ''}
                        onChange={(e) => handleParameterChange(norm.parameter_name, e.target.value)}
                        placeholder={`${norm.min_value} - ${norm.max_value}`}
                      />
                      <div className="text-xs text-slate-500 mt-1">
                        Normal range: {norm.min_value} - {norm.max_value} {norm.unit}
                      </div>
                    </div>
                  );
                })}

                {/* Setting Parameters */}
                {['Fuel to Brick Ratio - Setting', 'Brick Moisture % - Setting'].map(paramName => {
                  const norm = parameterNorms.find(n => n.parameter_name === paramName);
                  if (!norm) return null;
                  return (
                    <div key={norm.id}>
                      <Label htmlFor={norm.parameter_name}>
                        {norm.parameter_name} ({norm.unit})
                      </Label>
                      <Input
                        id={norm.parameter_name}
                        type="number"
                        step="0.01"
                        value={parameters[norm.parameter_name] || ''}
                        onChange={(e) => handleParameterChange(norm.parameter_name, e.target.value)}
                        placeholder={`${norm.min_value} - ${norm.max_value}`}
                      />
                      <div className="text-xs text-slate-500 mt-1">
                        Normal range: {norm.min_value} - {norm.max_value} {norm.unit}
                      </div>
                    </div>
                  );
                })}

                {/* Brick Weight Fields */}
                <div>
                  <Label htmlFor="green_brick_weight">
                    Green Brick Weight (kg)
                  </Label>
                  <Input
                    id="green_brick_weight"
                    type="number"
                    step="0.01"
                    value={greenBrickWeight}
                    onChange={(e) => setGreenBrickWeight(e.target.value)}
                    placeholder="Enter green brick weight"
                  />
                </div>

                <div>
                  <Label htmlFor="fired_brick_weight">
                    Fired Brick Weight (kg)
                  </Label>
                  <Input
                    id="fired_brick_weight"
                    type="number"
                    step="0.01"
                    value={firedBrickWeight}
                    onChange={(e) => setFiredBrickWeight(e.target.value)}
                    placeholder="Enter fired brick weight"
                  />
                </div>
              </div>

              <Button onClick={handleSubmit} className="w-full">
                Record Measurement
              </Button>
            </CardContent>
          </Card>

          {/* Chamber Zone Configuration */}
          {selectedKiln && (
            <Card>
              <CardHeader>
                <CardTitle>Chamber Zone Configuration - {selectedKilnConfig?.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                  {selectedKilnConfig && Array.from({ length: selectedKilnConfig.chambers }, (_, i) => i + 1).map(chamber => (
                    <div key={chamber} className="space-y-2">
                      <Label className="text-xs">Chamber {chamber}</Label>
                      <Select
                        value={getZoneForChamber(selectedKiln, chamber)}
                        onValueChange={(zone) => handleZoneChange(selectedKiln, chamber, zone)}
                      >
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {ZONES.map(zone => (
                            <SelectItem key={zone} value={zone} className="text-xs">
                              {zone}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="overview" className="mt-6">
          <ActiveAlerts />
          <KilnZoneOverview showLegend={false} />
        </TabsContent>

        <TabsContent value="trends" className="mt-6">
          <ParameterTrends />
        </TabsContent>

        <TabsContent value="zone-control" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Zone Control</CardTitle>
              <div className="flex items-center gap-4">
                <Label>Kiln Selection:</Label>
                <Select value={selectedKiln} onValueChange={setSelectedKiln}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select kiln" />
                  </SelectTrigger>
                  <SelectContent>
                    {KILN_OPTIONS.map(kiln => (
                      <SelectItem key={kiln.id} value={kiln.id}>
                        {kiln.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              {selectedKiln ? (
                <div className="space-y-6">
                  {/* Zone Control Interface */}
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-10 gap-2">
                    {selectedKilnConfig && Array.from({ length: selectedKilnConfig.chambers }, (_, i) => i + 1).map(chamber => {
                      const zone = getZoneForChamber(selectedKiln, chamber);
                    const zoneColor = {
                      "Inactive": "#e2e8f0",
                      "Preheat 1": "#93c5fd",
                      "Preheat 2": "#60a5fa",
                      "Pre-combustion": "#eab308",
                      "Firing": "#f97316",
                      "Cooling 1": "#dc2626",
                      "Cooling 2": "#fca5a5",
                      "Setting": "#16a34a",
                      "Dehacking": "#fca5a5"
                    }[zone] || "#e2e8f0";

                    return (
                      <div key={chamber} className="space-y-2">
                        <div
                          className="border rounded p-3 text-center min-h-[80px] flex flex-col justify-center cursor-pointer hover:shadow-md transition-shadow"
                          style={{ backgroundColor: zoneColor }}
                        >
                          <div className="text-sm font-semibold mb-1">Ch {chamber}</div>
                          <div className="text-xs text-gray-700 font-medium">
                            {zone === "Preheat 1" ? "Setting..." :
                             zone === "Preheat 2" ? "Preheat..." :
                             zone === "Pre-combustion" ? "Pre-..." :
                             zone === "Cooling 1" ? "Cooling..." :
                             zone === "Cooling 2" ? "Cooling..." :
                             zone === "Setting" ? "Setting..." :
                             zone === "Dehacking" ? "Dehacking..." :
                             zone === "Firing" ? "Firing..." :
                             "Inactive"}
                          </div>
                        </div>
                        <Select
                          value={zone}
                          onValueChange={(newZone) => handleZoneChange(selectedKiln, chamber, newZone)}
                        >
                          <SelectTrigger className="h-8 text-xs">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {ZONES.map(zoneOption => (
                              <SelectItem key={zoneOption} value={zoneOption} className="text-xs">
                                {zoneOption}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    );
                  })}
                </div>

                {/* Zone Color Legend */}
                <div className="border-t pt-4">
                  <h4 className="font-semibold mb-3">Zone Color Legend</h4>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:flex lg:flex-wrap gap-2 sm:gap-4">
                    {[
                      { zone: "Inactive", color: "#e2e8f0" },
                      { zone: "Preheat 1", color: "#93c5fd" },
                      { zone: "Preheat 2", color: "#60a5fa" },
                      { zone: "Pre-combustion", color: "#eab308" },
                      { zone: "Firing", color: "#f97316" },
                      { zone: "Cooling 1", color: "#dc2626" },
                      { zone: "Cooling 2", color: "#fca5a5" },
                      { zone: "Setting", color: "#16a34a" },
                      { zone: "Dehacking", color: "#fca5a5" }
                    ].map(({ zone, color }) => (
                      <div key={zone} className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded border"
                          style={{ backgroundColor: color }}
                        />
                        <span className="text-sm">{zone}</span>
                      </div>
                    ))}
                  </div>
                </div>
                </div>
              ) : (
                <div className="text-center text-slate-500 py-8">
                  Please select a kiln to manage zone assignments
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="daily-summary" className="mt-6">
          <ChamberProductionTable selectedDate={format(new Date(), 'yyyy-MM-dd')} />
        </TabsContent>

        <TabsContent value="parameter-norms" className="mt-6">
          <EnhancedParameterNorms />
        </TabsContent>
      </Tabs>
    </div>
  );
};
