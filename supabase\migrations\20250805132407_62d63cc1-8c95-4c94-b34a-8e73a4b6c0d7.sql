
-- Create a storage bucket for media files
INSERT INTO storage.buckets (id, name, public)
VALUES ('media-files', 'media-files', true);

-- Create policy to allow authenticated users to view media files
CREATE POLICY "Allow authenticated users to view media files" ON storage.objects
FOR SELECT USING (bucket_id = 'media-files' AND auth.role() = 'authenticated');

-- Create policy to allow authenticated users to upload media files
CREATE POLICY "Allow authenticated users to upload media files" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'media-files' AND auth.role() = 'authenticated');

-- Create policy to allow authenticated users to update their own media files
CREATE POLICY "Allow authenticated users to update media files" ON storage.objects
FOR UPDATE USING (bucket_id = 'media-files' AND auth.role() = 'authenticated');

-- Create policy to allow authenticated users to delete media files
CREATE POLICY "Allow authenticated users to delete media files" ON storage.objects
FOR DELETE USING (bucket_id = 'media-files' AND auth.role() = 'authenticated');
