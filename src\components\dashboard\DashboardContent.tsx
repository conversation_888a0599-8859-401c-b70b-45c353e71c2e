import { useState } from "react";
import { TimeRangeSelector } from "./TimeRangeSelector";
import { KeyMetrics } from "./KeyMetrics";
import { AnalyticsCharts } from "./AnalyticsCharts";
import { ProductionBreakdownTable } from "./ProductionBreakdownTable";
import { QuickAccessCards } from "./QuickAccessCards";
import { DailyActivitySummary } from "./DailyActivitySummary";
import { DehackingBreakdownCard } from "./cards/DehackingBreakdownCard";
import { ProductionLoss } from "./ProductionLoss";
import { FuelBunkersDashboard } from "./FuelBunkersDashboard";
import { FinishedStockSummaryCard } from "./cards/FinishedStockSummaryCard";
import { HacklineCountSummaryCard } from "./cards/HacklineCountSummaryCard";
import { LoadSummaryCard } from "./cards/LoadSummaryCard";
import { StockMovementSummaryCard } from "./cards/StockMovementSummaryCard";

export type TimeRange = 'today' | 'week' | 'month' | 'year' | 'custom';

export interface CustomDateRange {
  from: string;
  to: string;
}

export const DashboardContent = () => {
  const [timeRange, setTimeRange] = useState<TimeRange>('today');
  const [customDateRange, setCustomDateRange] = useState<CustomDateRange>({
    from: '',
    to: ''
  });

  return (
    <div className="space-y-6">
      {/* Quick Access Cards - Rearranged as requested */}
      <QuickAccessCards timeRange={timeRange} />

      {/* Time Range Selector - moved below QuickAccessCards as requested */}
      <TimeRangeSelector
        selectedRange={timeRange}
        onRangeChange={setTimeRange}
        customDateRange={customDateRange}
        onCustomDateChange={setCustomDateRange}
      />

      {/* Additional cards section - Finished Stock, Hackline Count, Load Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <FinishedStockSummaryCard />
        <HacklineCountSummaryCard />
        <LoadSummaryCard timeRange={timeRange} />
      </div>

      {/* Production Loss */}
      <ProductionLoss />

      {/* Production Breakdown (today) - Note: User requested to remove Production Breakdown card but keep this one for today */}
      <ProductionBreakdownTable timeRange="today" />

      {/* Dehacking Production (today) */}
      <DehackingBreakdownCard timeRange="today" />

      {/* Stock Yard Movement card */}
      <StockMovementSummaryCard />

      {/* Fuel Bunkers and Kiln Production Distribution */}
      <FuelBunkersDashboard />

      {/* Analytics Charts - contains Kiln Production Distribution */}
      <AnalyticsCharts timeRange={timeRange} />

      {/* Key Metrics - Total Pallets (Today), Total Employees, Active Employees, Projected Payroll */}
      <KeyMetrics timeRange="today" />

      {/* Activity Summary */}
      <DailyActivitySummary />
    </div>
  );
};
