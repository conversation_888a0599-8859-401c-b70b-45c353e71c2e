import { useState } from "react";
import { TimeRangeSelector } from "./TimeRangeSelector";
import { KeyMetrics } from "./KeyMetrics";
import { AnalyticsCharts } from "./AnalyticsCharts";
import { ProductionBreakdownTable } from "./ProductionBreakdownTable";
import { QuickAccessCards } from "./QuickAccessCards";
import { DailyActivitySummary } from "./DailyActivitySummary";
import { DehackingBreakdownCard } from "./cards/DehackingBreakdownCard";
import { ProductionLoss } from "./ProductionLoss";
import { FuelBunkersDashboard } from "./FuelBunkersDashboard";

export type TimeRange = 'today' | 'week' | 'month' | 'year' | 'custom';

export interface CustomDateRange {
  from: string;
  to: string;
}

export const DashboardContent = () => {
  const [timeRange, setTimeRange] = useState<TimeRange>('today');
  const [customDateRange, setCustomDateRange] = useState<CustomDateRange>({
    from: '',
    to: ''
  });

  return (
    <div className="space-y-6">
      {/* Quick Access Cards - Rearranged as requested */}
      <QuickAccessCards timeRange={timeRange} />
      
      {/* Time Range Selector */}
      <TimeRangeSelector 
        selectedRange={timeRange} 
        onRangeChange={setTimeRange}
        customDateRange={customDateRange}
        onCustomDateChange={setCustomDateRange}
      />
      
      {/* Key Metrics */}
      <KeyMetrics timeRange={timeRange} />
      
      {/* Analytics Charts */}
      <AnalyticsCharts timeRange={timeRange} />

      {/* Production Loss */}
      <ProductionLoss />

      {/* Dehacking Production */}
      <DehackingBreakdownCard timeRange={timeRange} />

      {/* Fuel Bunkers Dashboard */}
      <FuelBunkersDashboard />

      {/* Activity Summary */}
      <DailyActivitySummary />
    </div>
  );
};
