
-- Create a trigger function to populate chamber_persistent_data when setting production entries are created
CREATE OR REPLACE FUNCTION populate_chamber_persistent_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert or update chamber persistent data for setting operations
  INSERT INTO public.chamber_persistent_data (
    kiln_id,
    chamber_number,
    fire_id,
    brick_type_id,
    team_id,
    pallet_count,
    brick_count,
    operation_type,
    entry_date,
    entry_hour,
    is_active
  )
  VALUES (
    (SELECT kiln_id FROM fires WHERE id = NEW.fire_id),
    COALESCE(NEW.chamber_number, 1),
    NEW.fire_id,
    NEW.brick_type_id,
    NEW.team_id,
    NEW.pallet_count,
    NEW.pallet_count * COALESCE((SELECT bricks_per_pallet FROM management_brick_types WHERE id = NEW.brick_type_id), 0),
    'setting',
    NEW.date,
    COALESCE(NEW.hour, EXTRACT(hour FROM now())),
    true
  )
  ON CONFLICT (kiln_id, chamber_number, fire_id, brick_type_id, operation_type, entry_date, entry_hour) 
  DO UPDATE SET
    pallet_count = chamber_persistent_data.pallet_count + EXCLUDED.pallet_count,
    brick_count = chamber_persistent_data.brick_count + EXCLUDED.brick_count,
    updated_at = now();
    
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on setting_production_entries
DROP TRIGGER IF EXISTS trigger_populate_chamber_persistent_data ON setting_production_entries;
CREATE TRIGGER trigger_populate_chamber_persistent_data
  AFTER INSERT ON setting_production_entries
  FOR EACH ROW
  EXECUTE FUNCTION populate_chamber_persistent_data();

-- Populate existing data from setting_production_entries into chamber_persistent_data
INSERT INTO public.chamber_persistent_data (
  kiln_id,
  chamber_number,
  fire_id,
  brick_type_id,
  team_id,
  pallet_count,
  brick_count,
  operation_type,
  entry_date,
  entry_hour,
  is_active,
  created_at,
  updated_at
)
SELECT 
  f.kiln_id,
  COALESCE(spe.chamber_number, 1) as chamber_number,
  spe.fire_id,
  spe.brick_type_id,
  spe.team_id,
  SUM(spe.pallet_count) as pallet_count,
  SUM(spe.pallet_count * COALESCE(mbt.bricks_per_pallet, 0)) as brick_count,
  'setting' as operation_type,
  spe.date as entry_date,
  COALESCE(spe.hour, EXTRACT(hour FROM spe.created_at)) as entry_hour,
  true as is_active,
  MIN(spe.created_at) as created_at,
  MAX(spe.created_at) as updated_at
FROM setting_production_entries spe
JOIN fires f ON f.id = spe.fire_id
LEFT JOIN management_brick_types mbt ON mbt.id = spe.brick_type_id
WHERE NOT EXISTS (
  SELECT 1 FROM chamber_persistent_data cpd 
  WHERE cpd.kiln_id = f.kiln_id 
  AND cpd.chamber_number = COALESCE(spe.chamber_number, 1)
  AND cpd.fire_id = spe.fire_id
  AND cpd.brick_type_id = spe.brick_type_id
  AND cpd.operation_type = 'setting'
  AND cpd.entry_date = spe.date
  AND cpd.entry_hour = COALESCE(spe.hour, EXTRACT(hour FROM spe.created_at))
)
GROUP BY f.kiln_id, COALESCE(spe.chamber_number, 1), spe.fire_id, spe.brick_type_id, spe.team_id, spe.date, COALESCE(spe.hour, EXTRACT(hour FROM spe.created_at));
