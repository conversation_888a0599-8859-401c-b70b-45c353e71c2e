
-- First, let's check if we have the necessary data in related tables and fix the trigger
-- Drop the existing trigger and function to recreate them properly
DROP TRIGGER IF EXISTS trigger_populate_chamber_persistent_data ON setting_production_entries;
DROP FUNCTION IF EXISTS populate_chamber_persistent_data();

-- Recreate the function with better error handling
CREATE OR REPLACE FUNCTION populate_chamber_persistent_data()
RETURNS TRIGGER AS $$
DECLARE
  kiln_id_val text;
  brick_count_val integer;
BEGIN
  -- Get the kiln_id from the fire
  SELECT f.kiln_id INTO kiln_id_val
  FROM fires f 
  WHERE f.id = NEW.fire_id;
  
  -- If no kiln found, use a default or skip
  IF kiln_id_val IS NULL THEN
    RAISE NOTICE 'No kiln found for fire_id: %', NEW.fire_id;
    RETURN NEW;
  END IF;
  
  -- Calculate brick count
  SELECT COALESCE(NEW.pallet_count * mbt.bricks_per_pallet, NEW.pallet_count * 320)
  INTO brick_count_val
  FROM management_brick_types mbt 
  WHERE mbt.id = NEW.brick_type_id;
  
  -- If no brick type found, use default calculation
  IF brick_count_val IS NULL THEN
    brick_count_val := NEW.pallet_count * 320;
  END IF;
  
  -- Insert into chamber_persistent_data
  INSERT INTO public.chamber_persistent_data (
    kiln_id,
    chamber_number,
    fire_id,
    brick_type_id,
    team_id,
    pallet_count,
    brick_count,
    operation_type,
    entry_date,
    entry_hour,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    kiln_id_val,
    COALESCE(NEW.chamber_number, 1),
    NEW.fire_id,
    NEW.brick_type_id,
    NEW.team_id,
    NEW.pallet_count,
    brick_count_val,
    'setting',
    NEW.date,
    COALESCE(NEW.hour, EXTRACT(hour FROM now())::integer),
    true,
    now(),
    now()
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in populate_chamber_persistent_data: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER trigger_populate_chamber_persistent_data
  AFTER INSERT ON setting_production_entries
  FOR EACH ROW
  EXECUTE FUNCTION populate_chamber_persistent_data();

-- Clear existing data in chamber_persistent_data to avoid conflicts
DELETE FROM public.chamber_persistent_data WHERE operation_type = 'setting';

-- Backfill with a simpler approach
INSERT INTO public.chamber_persistent_data (
  kiln_id,
  chamber_number,
  fire_id,
  brick_type_id,
  team_id,
  pallet_count,
  brick_count,
  operation_type,
  entry_date,
  entry_hour,
  is_active,
  created_at,
  updated_at
)
SELECT 
  f.kiln_id,
  COALESCE(spe.chamber_number, 1),
  spe.fire_id,
  spe.brick_type_id,
  spe.team_id,
  spe.pallet_count,
  spe.pallet_count * COALESCE(mbt.bricks_per_pallet, 320),
  'setting',
  spe.date,
  COALESCE(spe.hour, EXTRACT(hour FROM spe.created_at)::integer),
  true,
  spe.created_at,
  spe.created_at
FROM setting_production_entries spe
JOIN fires f ON f.id = spe.fire_id
LEFT JOIN management_brick_types mbt ON mbt.id = spe.brick_type_id;
