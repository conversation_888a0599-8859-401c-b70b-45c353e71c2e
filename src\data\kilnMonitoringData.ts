
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

export type KilnParameterNorm = Database['public']['Tables']['kiln_parameter_norms']['Row'];
export type KilnMonitoringMeasurement = Database['public']['Tables']['kiln_monitoring_measurements']['Row'] & {
  green_brick_weight?: number;
  fired_brick_weight?: number;
};
export type KilnChamberZone = Database['public']['Tables']['kiln_chamber_zones']['Row'];
export type KilnMeasurementAction = Database['public']['Tables']['kiln_measurement_actions']['Row'];

export interface EnhancedKilnParameterNorm {
  id: string;
  parameter_name: string;
  unit: string;
  min_value: number;
  max_value: number;
  action: string;
  cause: string;
  reasoning?: string;
  action_required?: string;
  last_action_taken?: string;
  last_action_by?: string;
  last_action_date?: string;
  created_at: string;
  updated_at: string;
}

export interface DailySummary {
  date: string;
  time_slots: any[];
  total_measurements: number;
  parameters_out_of_norm: number;
  actions_taken: number;
}

export interface KilnMeasurementWithActions extends KilnMonitoringMeasurement {
  actions?: KilnMeasurementAction[];
}

// Parameter Norms functions
export const getParameterNorms = async (userId?: string): Promise<KilnParameterNorm[]> => {
  console.log('Getting parameter norms for user:', userId);
  
  const { data, error } = await supabase
    .from('kiln_parameter_norms')
    .select('*')
    .order('parameter_name');

  if (error) {
    console.error('Error fetching parameter norms:', error);
    throw error;
  }

  return data || [];
};

export const updateParameterNorm = async (id: string, updates: Partial<KilnParameterNorm>): Promise<KilnParameterNorm> => {
  const { data, error } = await supabase
    .from('kiln_parameter_norms')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating parameter norm:', error);
    throw error;
  }

  return data;
};

// Enhanced parameter norms functions
export const getEnhancedParameterNorms = async (userId?: string): Promise<EnhancedKilnParameterNorm[]> => {
  console.log('Getting enhanced parameter norms for user:', userId);
  
  const { data, error } = await supabase
    .from('kiln_parameter_norms')
    .select('*')
    .order('parameter_name');

  if (error) {
    console.error('Error fetching enhanced parameter norms:', error);
    throw error;
  }

  return (data || []).map(norm => ({
    id: norm.id,
    parameter_name: norm.parameter_name,
    unit: norm.unit,
    min_value: norm.min_value,
    max_value: norm.max_value,
    action: norm.action,
    cause: norm.cause,
    reasoning: norm.reasoning || undefined,
    action_required: norm.action_required || undefined,
    last_action_taken: norm.last_action_taken || undefined,
    last_action_by: norm.last_action_by || undefined,
    last_action_date: norm.last_action_date || undefined,
    created_at: norm.created_at,
    updated_at: norm.updated_at
  }));
};

export const updateEnhancedParameterNorm = async (id: string, updates: Partial<EnhancedKilnParameterNorm>): Promise<EnhancedKilnParameterNorm> => {
  const { data, error } = await supabase
    .from('kiln_parameter_norms')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating enhanced parameter norm:', error);
    throw error;
  }

  return {
    id: data.id,
    parameter_name: data.parameter_name,
    unit: data.unit,
    min_value: data.min_value,
    max_value: data.max_value,
    action: data.action,
    cause: data.cause,
    reasoning: data.reasoning || undefined,
    action_required: data.action_required || undefined,
    last_action_taken: data.last_action_taken || undefined,
    last_action_by: data.last_action_by || undefined,
    last_action_date: data.last_action_date || undefined,
    created_at: data.created_at,
    updated_at: data.updated_at
  };
};

// Measurements functions
export const getMeasurements = async (
  filters?: {
    kiln_id?: string;
    date?: string;
    parameter?: string;
  },
  userId?: string
): Promise<KilnMeasurementWithActions[]> => {
  console.log('Getting measurements with filters:', filters, 'for user:', userId);
  
  let query = supabase
    .from('kiln_monitoring_measurements')
    .select('*')
    .order('measurement_date', { ascending: false })
    .order('measurement_time', { ascending: false });

  if (filters?.kiln_id) {
    query = query.eq('kiln_id', filters.kiln_id);
  }

  if (filters?.date) {
    query = query.eq('measurement_date', filters.date);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching measurements:', error);
    throw error;
  }

  // Transform data to include optional weight properties
  const measurements: KilnMeasurementWithActions[] = (data || []).map(measurement => ({
    ...measurement,
    green_brick_weight: (measurement.parameters as any)?.green_brick_weight,
    fired_brick_weight: (measurement.parameters as any)?.fired_brick_weight,
  }));

  // Get actions for each measurement
  const measurementIds = measurements.map(m => m.id);
  if (measurementIds.length > 0) {
    const { data: actionsData } = await supabase
      .from('kiln_measurement_actions')
      .select('*')
      .in('measurement_id', measurementIds);

    // Group actions by measurement_id
    const actionsByMeasurement = (actionsData || []).reduce((acc, action) => {
      if (!acc[action.measurement_id]) {
        acc[action.measurement_id] = [];
      }
      acc[action.measurement_id].push(action);
      return acc;
    }, {} as Record<string, KilnMeasurementAction[]>);

    // Add actions to measurements
    measurements.forEach(measurement => {
      measurement.actions = actionsByMeasurement[measurement.id] || [];
    });
  }

  return measurements;
};

export const createMeasurement = async (
  measurement: Omit<KilnMonitoringMeasurement, 'id' | 'created_at' | 'updated_at'>
): Promise<KilnMonitoringMeasurement> => {
  // Prepare parameters object including brick weights
  const baseParameters = (measurement.parameters as Record<string, any>) || {};
  const parameters = {
    ...baseParameters,
    ...(measurement.green_brick_weight && { green_brick_weight: measurement.green_brick_weight }),
    ...(measurement.fired_brick_weight && { fired_brick_weight: measurement.fired_brick_weight })
  };

  const measurementData = {
    kiln_id: measurement.kiln_id,
    chamber_number: measurement.chamber_number,
    fire_zone: measurement.fire_zone,
    measurement_time: measurement.measurement_time,
    measurement_date: measurement.measurement_date,
    parameters,
    user_id: measurement.user_id
  };

  const { data, error } = await supabase
    .from('kiln_monitoring_measurements')
    .insert(measurementData)
    .select()
    .single();

  if (error) {
    console.error('Error creating measurement:', error);
    throw error;
  }

  return {
    ...data,
    green_brick_weight: (data.parameters as any)?.green_brick_weight,
    fired_brick_weight: (data.parameters as any)?.fired_brick_weight,
  };
};

// Chamber zones functions
export const getChamberZones = async (): Promise<KilnChamberZone[]> => {
  const { data, error } = await supabase
    .from('kiln_chamber_zones')
    .select('*')
    .order('kiln_id')
    .order('chamber_number');

  if (error) {
    console.error('Error fetching chamber zones:', error);
    throw error;
  }

  return data || [];
};

export const updateChamberZone = async (
  kiln_id: string,
  chamber_number: number,
  zone: string
): Promise<KilnChamberZone> => {
  const { data, error } = await supabase
    .from('kiln_chamber_zones')
    .upsert({
      kiln_id,
      chamber_number,
      zone,
    }, {
      onConflict: 'kiln_id,chamber_number'
    })
    .select()
    .single();

  if (error) {
    console.error('Error updating chamber zone:', error);
    throw error;
  }

  return data;
};

export const initializeChamberZones = async (
  kiln_id: string,
  chamber_count: number
): Promise<KilnChamberZone[]> => {
  const zones = Array.from({ length: chamber_count }, (_, i) => ({
    kiln_id,
    chamber_number: i + 1,
    zone: 'Inactive',
  }));

  const { data, error } = await supabase
    .from('kiln_chamber_zones')
    .upsert(zones)
    .select();

  if (error) {
    console.error('Error initializing chamber zones:', error);
    throw error;
  }

  return data || [];
};

// Measurement actions functions
export const getMeasurementActions = async (measurementId: string): Promise<KilnMeasurementAction[]> => {
  const { data, error } = await supabase
    .from('kiln_measurement_actions')
    .select('*')
    .eq('measurement_id', measurementId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching measurement actions:', error);
    throw error;
  }

  return data || [];
};

export const createMeasurementAction = async (
  action: Omit<KilnMeasurementAction, 'id' | 'created_at' | 'updated_at'>
): Promise<KilnMeasurementAction> => {
  const { data, error } = await supabase
    .from('kiln_measurement_actions')
    .insert(action)
    .select()
    .single();

  if (error) {
    console.error('Error creating measurement action:', error);
    throw error;
  }

  return data;
};

export const updateMeasurementAction = async (
  id: string,
  updates: Partial<KilnMeasurementAction>
): Promise<KilnMeasurementAction> => {
  const { data, error } = await supabase
    .from('kiln_measurement_actions')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating measurement action:', error);
    throw error;
  }

  return data;
};

// Daily summary function
export const getDailySummary = async (date: string): Promise<DailySummary> => {
  const { data: measurements, error: measurementsError } = await supabase
    .from('kiln_monitoring_measurements')
    .select('*')
    .eq('measurement_date', date);

  if (measurementsError) {
    console.error('Error fetching measurements for daily summary:', measurementsError);
    throw measurementsError;
  }

  const { data: actions, error: actionsError } = await supabase
    .from('kiln_measurement_actions')
    .select('*')
    .gte('created_at', date + 'T00:00:00.000Z')
    .lt('created_at', date + 'T23:59:59.999Z');

  if (actionsError) {
    console.error('Error fetching actions for daily summary:', actionsError);
    throw actionsError;
  }

  const totalMeasurements = measurements?.length || 0;
  const actionsCount = actions?.length || 0;
  
  // Calculate parameters out of norm by checking if actions were created
  const parametersOutOfNorm = actions?.filter(action => action.is_out_of_norm).length || 0;

  return {
    date,
    time_slots: [], // This would be populated based on your time slot logic
    total_measurements: totalMeasurements,
    parameters_out_of_norm: parametersOutOfNorm,
    actions_taken: actionsCount
  };
};
