
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useQuery } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { format } from "date-fns";
import { Loader2 } from "lucide-react";
import { getKilns, type KilnConfig } from "@/data/kilnData";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

interface KilnForecastData {
  kiln_name: string;
  imperial_total: number;
  maxi_total: number;
}

interface KilnSettingSummaryData {
  kiln_name: string;
  imperial_count: number;
  maxi_count: number;
}

export const KilnSummaryTables = () => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();
  const today = format(new Date(), 'yyyy-MM-dd');

  // Fetch all kilns to ensure we show all kilns even if they have no data
  const { data: allKilns = [], isLoading: kilnsLoading } = useQuery<KilnConfig[]>({
    queryKey: ['kilns'],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getKilns(userId || undefined);
    },
    select: (data) => {
      // Sort kilns: Habla first, then Kiln 1-5
      return data.sort((a, b) => {
        if (a.name === 'Habla') return -1;
        if (b.name === 'Habla') return 1;

        const aNum = parseInt(a.name.replace('Kiln ', ''));
        const bNum = parseInt(b.name.replace('Kiln ', ''));
        return aNum - bNum;
      });
    }
  });

  // Kiln Forecast - Query setting_production_entries for active bricks in kilns (last 7 days)
  const { data: kilnForecast = [], isLoading: forecastLoading } = useQuery({
    queryKey: ['kiln-forecast', allKilns],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      console.log('🔍 Fetching kiln forecast from setting production entries...');

      // Get recent setting entries (last 7 days to show active bricks in kilns)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const sevenDaysAgoStr = format(sevenDaysAgo, 'yyyy-MM-dd');

      const { data: settingData, error } = await supabase
        .from('setting_production_entries')
        .select(`
          *,
          management_brick_types!inner(name, category, bricks_per_pallet),
          fires!inner(id, name, kiln_id, kilns!inner(id, name))
        `)
        .gte('date', sevenDaysAgoStr)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching setting production entries for forecast:', error);
        return [];
      }

      console.log('📊 Forecast setting entries fetched:', settingData?.length || 0, 'entries');

      // Initialize all kilns with zero values
      const kilnTotals = new Map<string, { imperial_total: number; maxi_total: number }>();
      allKilns.forEach(kiln => {
        kilnTotals.set(kiln.name, { imperial_total: 0, maxi_total: 0 });
      });

      // Process setting data - group by kiln and sum brick counts
      (settingData || []).forEach((entry: any) => {
        const kilnName = entry.fires?.kilns?.name || 'Unknown';
        const brickTypeName = entry.management_brick_types?.name || '';
        const brickCategory = entry.management_brick_types?.category || '';
        const brickCount = entry.pallet_count * (entry.management_brick_types?.bricks_per_pallet || 320);

        console.log(`📊 Forecast processing: kiln=${kilnName}, type="${brickTypeName}", category="${brickCategory}", count=${brickCount}`);

        if (!kilnTotals.has(kilnName)) {
          kilnTotals.set(kilnName, { imperial_total: 0, maxi_total: 0 });
        }

        const totals = kilnTotals.get(kilnName)!;
        const nameAndCategoryLower = `${brickTypeName} ${brickCategory}`.toLowerCase();

        if (nameAndCategoryLower.includes('imperial') || nameAndCategoryLower.includes('imp')) {
          totals.imperial_total += brickCount;
          console.log(`📊 Forecast added ${brickCount} Imperial to ${kilnName} (total: ${totals.imperial_total})`);
        } else if (nameAndCategoryLower.includes('maxi')) {
          totals.maxi_total += brickCount;
          console.log(`📊 Forecast added ${brickCount} Maxi to ${kilnName} (total: ${totals.maxi_total})`);
        } else {
          console.log(`📊 Forecast unmatched type/category "${brickTypeName}/${brickCategory}" for ${kilnName}, defaulting to Imperial`);
          totals.imperial_total += brickCount;
        }
      });

      // Convert to array format
      const forecast = allKilns.map(kiln => {
        const totals = kilnTotals.get(kiln.name) || { imperial_total: 0, maxi_total: 0 };
        return {
          kiln_name: kiln.name,
          imperial_total: totals.imperial_total,
          maxi_total: totals.maxi_total
        };
      });

      console.log('📊 Kiln forecast calculated:', forecast);
      return forecast;
    },
    enabled: allKilns.length > 0,
    refetchInterval: 300000, // Refetch every 5 minutes
  });

  // Setting Summary - fetch today's setting data exactly like dehacking summary structure
  const { data: settingSummary = [], isLoading: settingLoading } = useQuery({
    queryKey: ['kiln-setting-summary', today, allKilns],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      console.log('🔍 Fetching setting summary for date:', today);

      const { data, error } = await supabase
        .from('setting_production_entries')
        .select(`
          *,
          management_brick_types!inner(name, category, bricks_per_pallet),
          fires!inner(id, name, kiln_id, kilns!inner(id, name))
        `)
        .eq('date', today)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching setting summary:', error);
        return [];
      }

      console.log('📊 Setting summary fetched:', data?.length || 0, 'entries');

      if (!data || data.length === 0) {
        console.log('📊 No setting data found for today');
        // Return all kilns with zero values
        return allKilns.map(kiln => ({
          kiln_name: kiln.name,
          imperial_count: 0,
          maxi_count: 0
        }));
      }

      // Log all entries to debug brick type values
      data.forEach((entry: any, index: number) => {
        console.log(`📊 Setting entry ${index + 1}:`, {
          brick_type_id: entry.brick_type_id,
          brick_type_name: entry.management_brick_types?.name,
          category: entry.management_brick_types?.category,
          kiln: entry.fires?.kilns?.name,
          pallet_count: entry.pallet_count,
          bricks_per_pallet: entry.management_brick_types?.bricks_per_pallet
        });
      });

      // Group by kiln and calculate brick totals by category
      const grouped = data.reduce((acc: Record<string, { kiln_name: string; imperial_count: number; maxi_count: number }>, entry: any) => {
        const kilnName = entry.fires?.kilns?.name || 'Unknown';
        const brickTypeName = entry.management_brick_types?.name || '';
        const brickCategory = entry.management_brick_types?.category || '';
        
        if (!acc[kilnName]) {
          acc[kilnName] = {
            kiln_name: kilnName,
            imperial_count: 0,
            maxi_count: 0
          };
        }

        const brickCount = entry.pallet_count * (entry.management_brick_types?.bricks_per_pallet || 320);
        
        // Check both brick type name and category for classification
        const nameAndCategoryLower = `${brickTypeName} ${brickCategory}`.toLowerCase();
        console.log(`📊 Processing entry: kiln=${kilnName}, type="${brickTypeName}", category="${brickCategory}", combined="${nameAndCategoryLower}", count=${brickCount}`);
        
        if (nameAndCategoryLower.includes('imperial') || nameAndCategoryLower.includes('imp')) {
          acc[kilnName].imperial_count += brickCount;
          console.log(`📊 Added ${brickCount} to Imperial for ${kilnName} (total now: ${acc[kilnName].imperial_count})`);
        } else if (nameAndCategoryLower.includes('maxi')) {
          acc[kilnName].maxi_count += brickCount;
          console.log(`📊 Added ${brickCount} to Maxi for ${kilnName} (total now: ${acc[kilnName].maxi_count})`);
        } else {
          // Log unmatched categories for debugging
          console.log(`📊 Unmatched type/category "${brickTypeName}/${brickCategory}" for ${kilnName}, defaulting to Imperial`);
          acc[kilnName].imperial_count += brickCount;
        }

        return acc;
      }, {});

      // Ensure all kilns are included, even those with no data
      allKilns.forEach(kiln => {
        if (!grouped[kiln.name]) {
          grouped[kiln.name] = {
            kiln_name: kiln.name,
            imperial_count: 0,
            maxi_count: 0
          };
        }
      });

      // Convert to array and sort by kiln name (Habla first, then Kiln 1-5)
      const sorted = Object.values(grouped).sort((a, b) => {
        if (a.kiln_name === 'Habla') return -1;
        if (b.kiln_name === 'Habla') return 1;
        return a.kiln_name.localeCompare(b.kiln_name);
      });

      console.log('📊 Setting summary final result:', sorted);
      return sorted;
    },
    enabled: allKilns.length > 0,
    staleTime: 0,
    refetchOnWindowFocus: true,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const { data: dehackingSummary = [], isLoading: dehackingLoading } = useQuery({
    queryKey: ['kiln-dehacking-summary', today],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      console.log('🔍 Fetching dehacking summary for date:', today);

      const { data, error } = await supabase
        .from('dehacking_entries')
        .select(`
          *,
          management_brick_types!inner(name, category, grade, bricks_per_pallet),
          fires!inner(id, name, kiln_id, kilns!inner(id, name))
        `)
        .eq('date', today)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching dehacking summary:', error);
        return [];
      }

      console.log('📊 Dehacking summary fetched:', data?.length || 0, 'entries');

      if (!data || data.length === 0) {
        console.log('📊 No dehacking data found for today');
        return [];
      }

      // Group by kiln and fire
      const grouped = data.reduce((acc: any[], entry: any) => {
        const kilnName = entry.fires?.kilns?.name || 'Unknown';
        const fireName = entry.fires?.name || 'Unknown';
        const brickType = entry.management_brick_types?.category || 'Unknown';
        const grade = entry.management_brick_types?.grade || 'Unknown';
        
        let existing = acc.find(item => item.kiln_name === kilnName && item.fire_name === fireName);
        if (!existing) {
          existing = {
            kiln_name: kilnName,
            fire_name: fireName,
            nfx_imp_count: 0,
            nfp_imp_count: 0,
            second_grade_imp_count: 0,
            maxi_nfx_count: 0,
            maxi_nfp_count: 0,
            maxi_second_grade_count: 0
          };
          acc.push(existing);
        }

        const brickCount = entry.pallet_count * (entry.management_brick_types?.bricks_per_pallet || 0);
        
        if (brickType.toLowerCase().includes('imperial')) {
          if (grade.toLowerCase().includes('nfx')) {
            existing.nfx_imp_count += brickCount;
          } else if (grade.toLowerCase().includes('nfp')) {
            existing.nfp_imp_count += brickCount;
          } else if (grade.toLowerCase().includes('2nd') || grade.toLowerCase().includes('second')) {
            existing.second_grade_imp_count += brickCount;
          }
        } else if (brickType.toLowerCase().includes('maxi')) {
          if (grade.toLowerCase().includes('nfx')) {
            existing.maxi_nfx_count += brickCount;
          } else if (grade.toLowerCase().includes('nfp')) {
            existing.maxi_nfp_count += brickCount;
          } else if (grade.toLowerCase().includes('2nd') || grade.toLowerCase().includes('second')) {
            existing.maxi_second_grade_count += brickCount;
          }
        }

        return acc;
      }, []);

      // Sort by kiln name
      const sorted = grouped.sort((a, b) => {
        if (a.kiln_name === 'Habla') return -1;
        if (b.kiln_name === 'Habla') return 1;
        return a.kiln_name.localeCompare(b.kiln_name);
      });

      console.log('📊 Dehacking summary grouped:', sorted);
      return sorted;
    },
    staleTime: 0,
    refetchOnWindowFocus: true,
    refetchInterval: 30000, // Refetch every 30 seconds for live data
  });

  if (settingLoading || dehackingLoading || forecastLoading || kilnsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
        <p className="ml-2 text-slate-500">Loading summary data...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Kiln Forecast Table */}
      <Card>
        <CardHeader>
          <CardTitle>Kiln Forecast - Bricks Currently in Kilns</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Kiln</TableHead>
                <TableHead className="text-right">Imperial Total</TableHead>
                <TableHead className="text-right">Maxi Total</TableHead>
                <TableHead className="text-right">Grand Total</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {kilnForecast.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{item.kiln_name}</TableCell>
                  <TableCell className="text-right">
                    {item.imperial_total.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.maxi_total.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right font-semibold">
                    {(item.imperial_total + item.maxi_total).toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
              {kilnForecast.length === 0 && (
                <TableRow>
                  <TableCell colSpan={4} className="text-center text-slate-500">
                    No forecast data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Setting Summary Table */}
      <Card>
        <CardHeader>
          <CardTitle>Setting Summary - {format(new Date(), 'dd MMM yyyy')}</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Kiln</TableHead>
                <TableHead className="text-right">Imperial</TableHead>
                <TableHead className="text-right">Maxi</TableHead>
                <TableHead className="text-right">Total</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {settingSummary.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{item.kiln_name}</TableCell>
                  <TableCell className="text-right">
                    {item.imperial_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.maxi_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right font-semibold">
                    {(item.imperial_count + item.maxi_count).toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
              {settingSummary.length === 0 && (
                <TableRow>
                  <TableCell colSpan={4} className="text-center text-slate-500">
                    No setting data for today
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Dehacking Summary Table */}
      <Card>
        <CardHeader>
          <CardTitle>Dehacking Summary - {format(new Date(), 'dd MMM yyyy')}</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Kiln</TableHead>
                <TableHead className="text-right">NFX IMP</TableHead>
                <TableHead className="text-right">NFP IMP</TableHead>
                <TableHead className="text-right">2nd Grade IMP</TableHead>
                <TableHead className="text-right">Maxi NFX</TableHead>
                <TableHead className="text-right">Maxi NFP</TableHead>
                <TableHead className="text-right">Maxi 2nd Grade</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {dehackingSummary.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">
                    {item.kiln_name} ({item.fire_name})
                  </TableCell>
                  <TableCell className="text-right">
                    {item.nfx_imp_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.nfp_imp_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.second_grade_imp_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.maxi_nfx_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.maxi_nfp_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.maxi_second_grade_count.toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
              {dehackingSummary.length === 0 && (
                <TableRow>
                  <TableCell colSpan={7} className="text-center text-slate-500">
                    No dehacking data for today
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};
