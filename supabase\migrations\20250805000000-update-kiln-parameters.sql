-- Migration to update kiln monitoring parameters
-- Remove old parameters and add new ones as specified

-- First, remove the old parameters that are no longer needed
DELETE FROM public.kiln_parameter_norms 
WHERE parameter_name IN ('Brick Core Temp', 'CO');

-- Update existing parameters or add new ones
-- Delete existing parameters that need to be replaced/updated
DELETE FROM public.kiln_parameter_norms 
WHERE parameter_name IN (
  'Pre-heat Zone 1 Temp',
  'Pre-heat Zone 2 Temp', 
  'Pre-combustion Zone Temp',
  'Cooling Zone 1 Temp',
  'Cooling Zone 2 Temp',
  'Fire Position in Chamber',
  'Fire Movement',
  'O2',
  'CO2',
  'Fuel to Brick Ratio - Setting',
  'Brick Moisture % - Setting'
);

-- Insert the new parameter norms in the specified order
INSERT INTO public.kiln_parameter_norms (parameter_name, unit, min_value, max_value, cause, action, reasoning, action_required) VALUES

-- Temperature parameters
('Pre-heat Zone 1 Temp', '°C', 200, 350, 'Insufficient heat transfer or poor fuel distribution', 'Check fuel supply and adjust air flow', 'Pre-heat zone 1 is critical for initial brick warming and moisture removal. Too low temperatures result in thermal shock, too high can cause rapid moisture expansion.', 'Monitor fuel flow rates and adjust primary air dampers'),

('Pre-heat Zone 2 Temp', '°C', 350, 500, 'Heat transfer issues or air flow problems', 'Adjust secondary air and check heat distribution', 'Pre-heat zone 2 continues the gradual heating process. Proper temperature ensures even heat distribution before combustion zone.', 'Check secondary air dampers and heat distribution patterns'),

('Pre-combustion Zone Temp', '°C', 500, 650, 'Fuel-air mixture imbalance or combustion timing issues', 'Optimize fuel-air ratio and check ignition timing', 'Pre-combustion zone prepares bricks for main firing. Temperature control prevents thermal shock and ensures proper chemical reactions.', 'Monitor fuel injection timing and air-fuel mixture ratios'),

('Fire Zone Temp', '°C', 850, 950, 'Fuel/air imbalance or combustion efficiency issues', 'Tune fuel-air mix and check burner performance', 'Fire zone temperature is critical for proper brick firing and strength development. Optimal range ensures complete ceramic reactions.', 'Monitor burner performance and adjust fuel-air ratios'),

('Cooling Zone 1 Temp', '°C', 400, 600, 'Cooling rate too fast or air flow issues', 'Adjust cooling air flow and check dampers', 'Initial cooling zone prevents thermal shock. Controlled cooling prevents stress fractures and maintains brick integrity.', 'Monitor cooling air dampers and temperature gradients'),

('Cooling Zone 2 Temp', '°C', 50, 120, 'Excessive cooling air or poor temperature control', 'Adjust final cooling air and check exit conditions', 'Final cooling zone ensures bricks reach safe handling temperature. Proper cooling prevents cracking and warping.', 'Check final cooling air flow and exit temperature monitoring'),

-- Operational parameters  
('Fire Position in Chamber', 'm', 0, 25, 'Fire advancement issues or fuel distribution problems', 'Check fuel distribution and fire advancement rate', 'Fire position indicates proper progression through kiln. Delayed or advanced fire affects brick quality and energy efficiency.', 'Monitor fire advancement rate and fuel distribution patterns'),

('Fire Movement', 'm/day', 2.5, 3.75, 'Fire speed too fast or slow affecting brick quality', 'Adjust fuel rate and air flow to control fire speed', 'Fire movement rate affects residence time and heat treatment. Proper speed ensures complete firing without over/under-firing.', 'Monitor and adjust fuel feed rate and primary air flow'),

-- Combustion parameters
('O2', '%', 3, 5, 'Excess air or insufficient combustion air', 'Adjust air dampers and check air leaks', 'Oxygen levels indicate combustion efficiency. Proper levels ensure complete fuel combustion and optimal heat generation.', 'Monitor air damper positions and check for air leaks in system'),

('CO2', '%', 12, 15, 'Incomplete combustion or air infiltration', 'Optimize fuel-air ratio and seal air leaks', 'Carbon dioxide levels indicate combustion completeness. Proper levels ensure efficient fuel utilization and heat generation.', 'Check fuel-air mixing and seal any air infiltration points'),

('Draught Pressure', 'mmWC', -8, -4, 'Fan issues, blockages, or air leaks', 'Check fan operation and inspect for blockages', 'Proper draught ensures adequate air flow for combustion and heat distribution throughout the kiln.', 'Inspect fan operation, check for blockages, and seal air leaks'),

-- Setting parameters
('Fuel to Brick Ratio - Setting', 'kg/1000 bricks', 80, 120, 'Fuel efficiency issues or heat loss', 'Optimize fuel consumption and check heat retention', 'Fuel to brick ratio indicates energy efficiency. Proper ratio ensures adequate heat while minimizing fuel consumption.', 'Monitor fuel consumption rates and check kiln insulation'),

('Brick Moisture % - Setting', '%', 8, 10, 'Drying issues or loading problems', 'Improve drying process and check loading patterns', 'Proper moisture content ensures even drying and prevents cracking during firing. High moisture can cause steam explosions.', 'Monitor drying conditions and optimize loading patterns');
