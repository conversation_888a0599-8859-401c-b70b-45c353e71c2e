import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Calendar, Package, Truck, Edit, CheckCircle, Star, RotateCcw, X } from 'lucide-react';
import { format, eachDayOfInterval, startOfWeek, endOfWeek } from 'date-fns';
import { LoadPlanningEntry } from '@/types/loadPlanning';
import { filterLoadsByDateRange, getLoadsForDay } from '@/utils/loadPlanningDateUtils';

interface WeeklyViewNewProps {
  loads: LoadPlanningEntry[];
  currentDate: Date;
  isLoading: boolean;
  onEditLoad: (load: LoadPlanningEntry) => void;
  onRescheduleLoad: (load: LoadPlanningEntry) => void;
  onMarkAsReady: (loadId: string) => void;
  onMarkAsDispatched: (loadId: string) => void;
  onCancelLoad: (loadId: string) => void;
  isMarkingReady: boolean;
  isMarkingDispatched: boolean;
}

export const WeeklyViewNew: React.FC<WeeklyViewNewProps> = ({
  loads,
  currentDate,
  isLoading,
  onEditLoad,
  onRescheduleLoad,
  onMarkAsReady,
  onMarkAsDispatched,
  onCancelLoad,
  isMarkingReady,
  isMarkingDispatched
}) => {
  const weeklyLoads = filterLoadsByDateRange(loads, currentDate, 'weekly');
  
  // Get all days of the current week
  const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 }); // Monday
  const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 }); // Sunday
  const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });

  const getStatusBadge = (load: LoadPlanningEntry) => {
    if (load.dispatched) {
      return <Badge variant="default" className="bg-green-500">Dispatched</Badge>;
    }
    if (load.ready) {
      return <Badge variant="secondary" className="bg-blue-500 text-white">Ready</Badge>;
    }
    return <Badge variant="outline">Scheduled</Badge>;
  };

  const getRankBadge = (rank?: number) => {
    if (!rank) return null;
    
    const colors = {
      1: "bg-red-500 text-white",
      2: "bg-orange-500 text-white", 
      3: "bg-yellow-500 text-white",
    };
    
    const colorClass = colors[rank as keyof typeof colors] || "bg-gray-500 text-white";
    
    return (
      <Badge className={`${colorClass} flex items-center gap-1`}>
        <Star className="h-3 w-3" />
        {rank}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-7 gap-4">
        {weekDays.map((day, index) => (
          <Card key={index} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-slate-800">
            Week of {format(weekStart, 'MMMM d')} - {format(weekEnd, 'MMMM d, yyyy')}
          </h3>
          <div className="text-sm text-slate-600">
            {weeklyLoads.length} total load{weeklyLoads.length !== 1 ? 's' : ''}
          </div>
        </div>

      <div className="grid grid-cols-7 gap-4">
        {weekDays.map((day) => {
          const dayLoads = getLoadsForDay(weeklyLoads, day);
          const isToday = format(day, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');
          
          return (
            <Card key={day.toISOString()} className={`${isToday ? 'ring-2 ring-blue-500' : ''}`}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  <div className="flex flex-col items-center">
                    <span className="text-xs text-slate-500 uppercase">
                      {format(day, 'EEE')}
                    </span>
                    <span className={`text-lg ${isToday ? 'text-blue-600 font-bold' : ''}`}>
                      {format(day, 'd')}
                    </span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                {dayLoads.length === 0 ? (
                  <div className="text-center py-4">
                    <Calendar className="h-6 w-6 text-gray-300 mx-auto mb-1" />
                    <p className="text-xs text-gray-400">No loads</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {dayLoads.map((load) => (
                      <div
                        key={load.id}
                        className="p-2 bg-slate-50 rounded-md border hover:bg-slate-100 transition-colors"
                      >
                        <div className="flex items-start justify-between mb-1">
                          <div className="flex-1 min-w-0">
                            <p className="text-xs font-medium text-slate-800 truncate">
                              {load.client_name}
                            </p>
                            <div className="flex items-center gap-1 mt-1">
                              {getRankBadge(load.rank)}
                              {getStatusBadge(load)}
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-xs text-slate-600 space-y-1">
                          <div className="flex items-center gap-1">
                            <Truck className="h-3 w-3" />
                            <span className="truncate">{load.transporter}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Package className="h-3 w-3" />
                            <span>{load.brick_count} {load.load_type}</span>
                          </div>
                        </div>

                        {!load.dispatched && (
                          <div className="flex items-center gap-1 mt-2">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => onEditLoad(load)}
                                  className="h-6 w-6 p-0"
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Edit Load</p>
                              </TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => onRescheduleLoad(load)}
                                  className="h-6 w-6 p-0"
                                >
                                  <RotateCcw className="h-3 w-3" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Reschedule Load</p>
                              </TooltipContent>
                            </Tooltip>

                            {!load.ready ? (
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => onMarkAsReady(load.id)}
                                    disabled={isMarkingReady}
                                    className="h-6 w-6 p-0 text-blue-600 border-blue-600 hover:bg-blue-50"
                                  >
                                    <Star className="h-3 w-3" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Mark as Ready</p>
                                </TooltipContent>
                              </Tooltip>
                            ) : (
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => onMarkAsDispatched(load.id)}
                                    disabled={isMarkingDispatched}
                                    className="h-6 w-6 p-0 text-green-600 border-green-600 hover:bg-green-50"
                                  >
                                    <CheckCircle className="h-3 w-3" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Mark as Dispatched</p>
                                </TooltipContent>
                              </Tooltip>
                            )}

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => onCancelLoad(load.id)}
                                  className="h-6 w-6 p-0 text-red-600 border-red-600 hover:bg-red-50"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Cancel Load</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
      </div>
    </TooltipProvider>
  );
};
