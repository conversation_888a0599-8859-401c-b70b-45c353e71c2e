
export interface EnhancedKilnParameterNorm {
  id: string;
  parameter_name: string;
  unit: string;
  min_value: number;
  max_value: number;
  cause: string;
  action: string;
  reasoning?: string;
  action_required?: string;
  last_action_taken?: string;
  last_action_date?: string;
  last_action_by?: string;
  created_at: string;
  updated_at: string;
}

export interface KilnMeasurementAction {
  id: string;
  measurement_id: string;
  parameter_name: string;
  reading_value: number;
  is_out_of_norm: boolean;
  reasoning?: string;
  action_required?: string;
  action_taken?: string;
  action_date?: string;
  action_by?: string;
  created_at: string;
  updated_at: string;
}

export interface DailySummaryTimeSlot {
  time_slot: string;
  kilns: {
    [kilnId: string]: {
      kiln_name: string;
      measurements: {
        [parameter: string]: {
          value: number;
          status: 'normal' | 'warning' | 'critical';
          action_taken?: string;
        };
      };
      tests_performed: string[];
      total_tests: number;
    };
  };
}

export interface DailySummary {
  date: string;
  time_slots: DailySummaryTimeSlot[];
  total_measurements: number;
  parameters_out_of_norm: number;
  actions_taken: number;
}

export interface KilnMonitoringMeasurement {
  id: string;
  kiln_id: string;
  chamber_number: number;
  fire_zone?: string;
  measurement_time: string;
  measurement_date: string;
  parameters: Record<string, number>;
  green_brick_weight?: number;
  fired_brick_weight?: number;
  user_id?: string;
  created_at: string;
  updated_at: string;
  actions?: KilnMeasurementAction[];
}

export interface KilnChamberZone {
  id: string;
  kiln_id: string;
  chamber_number: number;
  zone: string;
  created_at: string;
  updated_at: string;
}

// Time slots for daily monitoring
export const TIME_SLOTS = [
  '06:00', '08:00', '10:00', '12:00', 
  '14:00', '16:00', '18:00', '20:00', '22:00'
];

// Standard kiln configurations - matching database IDs
export const KILN_OPTIONS = [
  { id: "kiln_a", name: "Habla", chambers: 10 },
  { id: "kiln_b", name: "Kiln 1", chambers: 24 },
  { id: "kiln_c", name: "Kiln 2", chambers: 24 },
  { id: "kiln_d", name: "Kiln 3", chambers: 24 },
  { id: "kiln_e", name: "Kiln 4", chambers: 24 },
  { id: "kiln_f", name: "Kiln 5", chambers: 24 },
];

// Parameter categories for better organization
export const PARAMETER_CATEGORIES = {
  TEMPERATURE: ['Brick Core Temp', 'Fire Zone Temp', 'Preheat Temp', 'Cooling Zone Temp'],
  COMBUSTION: ['CO', 'CO₂', 'O₂'],
  PHYSICAL: ['Brick Moisture', 'Draught Pressure'],
  OPERATIONAL: ['Fire Position', 'Fire Travel Rate', 'Fuel to Brick Ratio']
};
