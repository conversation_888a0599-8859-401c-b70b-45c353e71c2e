
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Package, Loader2 } from "lucide-react";
import { useDehackingEntries } from "@/hooks/useProductionEntries";
import { useQuery } from "@tanstack/react-query";
import { getManagementBrickTypes } from "@/data/managementBrickTypes";

export const FinishedStockSummaryCard = () => {
  const { data: dehackingEntries = [], isLoading: isLoadingDehacking } = useDehackingEntries();

  const { data: brickTypes = [], isLoading: isLoadingBrickTypes } = useQuery({
    queryKey: ['managementBrickTypes'],
    queryFn: () => getManagementBrickTypes(),
  });

  const isLoading = isLoadingDehacking || isLoadingBrickTypes;

  // Filter to only show current day's data
  const today = new Date().toISOString().split('T')[0]; // Get current date in YYYY-MM-DD format
  const todayDehacking = dehackingEntries.filter(entry => entry.date.split('T')[0] === today);

  // Aggregate data by brick type for today only
  const aggregatedData = todayDehacking.reduce((acc, entry) => {
    const brickType = brickTypes.find(bt => bt.id === entry.brick_type_id);
    const brickTypeName = brickType ? brickType.name : 'Unknown';

    if (!acc[brickTypeName]) {
      acc[brickTypeName] = 0;
    }
    acc[brickTypeName] += entry.pallet_count;
    return acc;
  }, {} as Record<string, number>);

  const typeBreakdown = Object.entries(aggregatedData).map(([name, pallets]) => ({
    name,
    pallets,
  })).sort((a, b) => b.pallets - a.pallets);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-slate-800">Finished Stock</CardTitle>
        <p className="text-sm text-slate-600">Today's dehacked bricks by type</p>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
          </div>
        ) : (
          <div>
            <h4 className="flex items-center gap-2 font-semibold text-slate-700 mb-3">
              <Package size={16} /> 
              By Brick Type
            </h4>
            {typeBreakdown.length > 0 ? (
              <div className="space-y-2">
                {typeBreakdown.map(type => (
                  <div key={type.name} className="flex justify-between items-center py-2 px-3 bg-slate-50 rounded-md">
                    <span className="text-slate-700 font-medium">{type.name}</span>
                    <span className="font-semibold text-slate-800">{type.pallets.toLocaleString()} pallets</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-slate-300 mx-auto mb-3" />
                <p className="text-slate-500 font-medium">No dehacked bricks today</p>
                <p className="text-sm text-slate-400">No production data available</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
