
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, Calendar } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useMemo, useState } from "react";
import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";
import { useProductionEntries, useSettingProductionEntries, useDehackingEntries } from "@/hooks/useProductionEntries";
import { useHacklineCounts } from "@/hooks/useHacklineCounts";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, startOfDay, endOfDay } from "date-fns";

export const ProductionLoss = () => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();
  const [period, setPeriod] = useState('currentMonth');

  const { data: brickTypes = [], isLoading: isLoadingBrickTypes } = useQuery<ManagementBrickType[]>({
    queryKey: ["managementBrickTypes"],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getManagementBrickTypes(userId || undefined);
    },
  });

  const { data: productionEntries = [], isLoading: isLoadingProd } = useProductionEntries();
  const { data: settingProductionEntries = [], isLoading: isLoadingSet } = useSettingProductionEntries();
  const { data: dehackingEntries = [], isLoading: isLoadingDehack } = useDehackingEntries();
  const { data: hacklineCounts = [], isLoading: isLoadingHackline } = useHacklineCounts();

  const brickTypeMap = useMemo(() => {
    const map = new Map<string, number>();
    brickTypes.forEach(bt => {
      map.set(bt.id, bt.bricks_per_pallet);
    });
    return map;
  }, [brickTypes]);

  const getDateRange = useMemo(() => {
    const now = new Date();
    switch (period) {
      case 'today':
        return { start: startOfDay(now), end: endOfDay(now) };
      case 'currentWeek':
        return { start: startOfWeek(now), end: endOfWeek(now) };
      case 'currentMonth':
        return { start: startOfMonth(now), end: endOfMonth(now) };
      default:
        return { start: startOfMonth(now), end: endOfMonth(now) };
    }
  }, [period]);

  const filterEntriesByDate = (entries: any[]) => {
    return entries.filter(entry => {
      const entryDate = new Date(entry.date || entry.created_at);
      return entryDate >= getDateRange.start && entryDate <= getDateRange.end;
    });
  };

  const calculations = useMemo(() => {
    const filteredProduction = filterEntriesByDate(productionEntries || []);
    const filteredSetting = filterEntriesByDate(settingProductionEntries || []);
    const filteredDehacking = filterEntriesByDate(dehackingEntries || []);
    const filteredHackline = filterEntriesByDate(hacklineCounts || []);

    // Factory Output by type
    const factoryImperial = filteredProduction.reduce((sum, entry) => {
      if (entry.brick_type_id === 'imperial_extruded') {
        const bpp = brickTypeMap.get(entry.brick_type_id) || 0;
        return sum + entry.pallet_count * bpp;
      }
      return sum;
    }, 0);

    const factoryMaxi = filteredProduction.reduce((sum, entry) => {
      if (entry.brick_type_id === 'maxi_extruded') {
        const bpp = brickTypeMap.get(entry.brick_type_id) || 0;
        return sum + entry.pallet_count * bpp;
      }
      return sum;
    }, 0);

    // Setting Teams by type
    const settingImperial = filteredSetting.reduce((sum, entry) => {
      if (entry.brick_type_id === 'imperial_extruded') {
        const bpp = brickTypeMap.get(entry.brick_type_id) || 0;
        return sum + entry.pallet_count * bpp;
      }
      return sum;
    }, 0);

    const settingMaxi = filteredSetting.reduce((sum, entry) => {
      if (entry.brick_type_id === 'maxi_extruded') {
        const bpp = brickTypeMap.get(entry.brick_type_id) || 0;
        return sum + entry.pallet_count * bpp;
      }
      return sum;
    }, 0);

    // Dehacking by type
    const dehackingImperial = filteredDehacking.reduce((sum, entry) => {
      if (entry.brick_type_id === 'imperial_extruded') {
        const bpp = brickTypeMap.get(entry.brick_type_id) || 0;
        return sum + entry.pallet_count * bpp;
      }
      return sum;
    }, 0);

    const dehackingMaxi = filteredDehacking.reduce((sum, entry) => {
      if (entry.brick_type_id === 'maxi_extruded') {
        const bpp = brickTypeMap.get(entry.brick_type_id) || 0;
        return sum + entry.pallet_count * bpp;
      }
      return sum;
    }, 0);

    // Hackline Count by type
    const hacklineImperial = filteredHackline.reduce((sum, entry) => {
      if (entry.pallet_type === 'Imperial') {
        return sum + entry.count_total;
      }
      return sum;
    }, 0);

    const hacklineMaxi = filteredHackline.reduce((sum, entry) => {
      if (entry.pallet_type === 'Maxi') {
        return sum + entry.count_total;
      }
      return sum;
    }, 0);

    // New calculation logic: Green Brick Loss = Hackline Count + Factory Output - Setting Teams
    const greenBrickLossImperial = hacklineImperial + factoryImperial - settingImperial;
    const greenBrickLossMaxi = hacklineMaxi + factoryMaxi - settingMaxi;
    const greenBrickLossTotal = greenBrickLossImperial + greenBrickLossMaxi;

    // Breakage Loss = Setting Teams - Dehacking Bricks
    const breakageLossImperial = settingImperial - dehackingImperial;
    const breakageLossMaxi = settingMaxi - dehackingMaxi;
    const breakageLossTotal = breakageLossImperial + breakageLossMaxi;

    // Total Loss = Green Brick Loss + Breakage Loss
    const totalLoss = greenBrickLossTotal + breakageLossTotal;

    return {
      greenBrickLoss: {
        imperial: greenBrickLossImperial,
        maxi: greenBrickLossMaxi,
        total: greenBrickLossTotal
      },
      breakageLoss: {
        imperial: breakageLossImperial,
        maxi: breakageLossMaxi,
        total: breakageLossTotal
      },
      totalLoss
    };
  }, [productionEntries, settingProductionEntries, dehackingEntries, hacklineCounts, brickTypeMap, getDateRange]);

  const valueDisplay = (value: number) => (
    <span className={value > 0 ? "text-red-600 font-bold" : "text-green-700 font-bold"}>
      {value.toLocaleString()}
    </span>
  );
  
  const isLoading = isLoadingBrickTypes || isLoadingProd || isLoadingSet || isLoadingDehack || isLoadingHackline;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-slate-800 flex items-center gap-2">
              <AlertTriangle size={20} className="text-orange-500" />
              Production Loss
            </CardTitle>
            <p className="text-sm text-slate-600">Live losses across the production process</p>
          </div>
          <div className="flex items-center gap-2">
            <Calendar size={16} className="text-slate-500" />
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="currentWeek">This Week</SelectItem>
                <SelectItem value="currentMonth">This Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="py-6 text-center text-slate-500">Loading...</div>
        ) : (
          <div className="space-y-6">
            <div>
              <div className="font-semibold mb-2 text-slate-700 border-b pb-1">Green Brick Loss</div>
              <div className="space-y-2">
                <p>
                  {valueDisplay(calculations.greenBrickLoss.total)} bricks<br />
                  <span className="text-xs text-slate-500">Hackline Count + Factory Output - Setting Teams</span>
                </p>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="bg-slate-50 p-2 rounded">
                    <span className="font-medium">Imperial:</span> {valueDisplay(calculations.greenBrickLoss.imperial)} bricks
                  </div>
                  <div className="bg-slate-50 p-2 rounded">
                    <span className="font-medium">Maxi:</span> {valueDisplay(calculations.greenBrickLoss.maxi)} bricks
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div className="font-semibold mb-2 text-slate-700 border-b pb-1">Breakage Loss</div>
              <div className="space-y-2">
                <p>
                  {valueDisplay(calculations.breakageLoss.total)} bricks<br />
                  <span className="text-xs text-slate-500">Setting Teams - Dehacking Bricks</span>
                </p>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="bg-slate-50 p-2 rounded">
                    <span className="font-medium">Imperial:</span> {valueDisplay(calculations.breakageLoss.imperial)} bricks
                  </div>
                  <div className="bg-slate-50 p-2 rounded">
                    <span className="font-medium">Maxi:</span> {valueDisplay(calculations.breakageLoss.maxi)} bricks
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div className="font-semibold mb-2 text-slate-700 border-b pb-1">Total Loss</div>
              <p>
                {valueDisplay(calculations.totalLoss)} bricks<br />
                <span className="text-xs text-slate-500">Green Brick Loss + Breakage Loss</span>
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
