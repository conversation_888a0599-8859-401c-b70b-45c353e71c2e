
-- First, ensure the trigger function exists and is correct
CREATE OR REPLACE FUNCTION populate_chamber_persistent_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Get the kiln_id from the fire
  INSERT INTO public.chamber_persistent_data (
    kiln_id,
    chamber_number,
    fire_id,
    brick_type_id,
    team_id,
    pallet_count,
    brick_count,
    operation_type,
    entry_date,
    entry_hour,
    is_active,
    created_at,
    updated_at
  )
  SELECT 
    f.kiln_id,
    COALESCE(NEW.chamber_number, 1),
    NEW.fire_id,
    NEW.brick_type_id,
    NEW.team_id,
    NEW.pallet_count,
    NEW.pallet_count * COALESCE(mbt.bricks_per_pallet, 320), -- Default to 320 if not found
    'setting',
    NEW.date,
    COALESCE(NEW.hour, EXTRACT(hour FROM now())::integer),
    true,
    now(),
    now()
  FROM fires f
  LEFT JOIN management_brick_types mbt ON mbt.id = NEW.brick_type_id
  WHERE f.id = NEW.fire_id
  ON CONFLICT (kiln_id, chamber_number, fire_id, brick_type_id, operation_type, entry_date, entry_hour) 
  DO UPDATE SET
    pallet_count = chamber_persistent_data.pallet_count + EXCLUDED.pallet_count,
    brick_count = chamber_persistent_data.brick_count + EXCLUDED.brick_count,
    team_id = EXCLUDED.team_id,
    updated_at = now();
    
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists and create new one
DROP TRIGGER IF EXISTS trigger_populate_chamber_persistent_data ON setting_production_entries;
CREATE TRIGGER trigger_populate_chamber_persistent_data
  AFTER INSERT ON setting_production_entries
  FOR EACH ROW
  EXECUTE FUNCTION populate_chamber_persistent_data();

-- Backfill existing setting_production_entries data into chamber_persistent_data
INSERT INTO public.chamber_persistent_data (
  kiln_id,
  chamber_number,
  fire_id,
  brick_type_id,
  team_id,
  pallet_count,
  brick_count,
  operation_type,
  entry_date,
  entry_hour,
  is_active,
  created_at,
  updated_at
)
SELECT 
  f.kiln_id,
  COALESCE(spe.chamber_number, 1) as chamber_number,
  spe.fire_id,
  spe.brick_type_id,
  spe.team_id,
  SUM(spe.pallet_count) as pallet_count,
  SUM(spe.pallet_count * COALESCE(mbt.bricks_per_pallet, 320)) as brick_count, -- Default to 320 if not found
  'setting' as operation_type,
  spe.date as entry_date,
  COALESCE(spe.hour, EXTRACT(hour FROM spe.created_at)::integer) as entry_hour,
  true as is_active,
  MIN(spe.created_at) as created_at,
  MAX(spe.created_at) as updated_at
FROM setting_production_entries spe
JOIN fires f ON f.id = spe.fire_id
LEFT JOIN management_brick_types mbt ON mbt.id = spe.brick_type_id
GROUP BY 
  f.kiln_id, 
  COALESCE(spe.chamber_number, 1), 
  spe.fire_id, 
  spe.brick_type_id, 
  spe.team_id, 
  spe.date, 
  COALESCE(spe.hour, EXTRACT(hour FROM spe.created_at)::integer)
ON CONFLICT (kiln_id, chamber_number, fire_id, brick_type_id, operation_type, entry_date, entry_hour) 
DO UPDATE SET
  pallet_count = chamber_persistent_data.pallet_count + EXCLUDED.pallet_count,
  brick_count = chamber_persistent_data.brick_count + EXCLUDED.brick_count,
  team_id = EXCLUDED.team_id,
  updated_at = EXCLUDED.updated_at;
