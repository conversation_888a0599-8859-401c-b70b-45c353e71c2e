
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { TimeRange } from "../DashboardContent";
import { useQuery } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, eachDayOfInterval, eachWeekOfInterval } from "date-fns";
import { Loader2, Hammer } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

interface DehackingBreakdownCardProps {
  timeRange: TimeRange;
}

export const DehackingBreakdownCard = ({ timeRange }: DehackingBreakdownCardProps) => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  const { data, isLoading } = useQuery({
    queryKey: ['dehacking-breakdown', timeRange],
    queryFn: async () => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }
      const now = new Date();
      let dateRanges: { label: string; from: Date; to: Date }[] = [];

      if (timeRange === 'today') {
        dateRanges = [{ label: format(now, 'EEEE'), from: startOfDay(now), to: endOfDay(now) }];
      } else if (timeRange === 'week') {
        const weekStart = startOfWeek(now, { weekStartsOn: 1 });
        const weekEnd = endOfWeek(now, { weekStartsOn: 1 });
        const days = eachDayOfInterval({ start: weekStart, end: weekEnd });
        dateRanges = days.map(day => ({
          label: format(day, 'EEEE'),
          from: startOfDay(day),
          to: endOfDay(day)
        }));
      } else if (timeRange === 'month') {
        const monthStart = startOfMonth(now);
        const monthEnd = endOfMonth(now);
        const weeks = eachWeekOfInterval({ start: monthStart, end: monthEnd }, { weekStartsOn: 1 });
        dateRanges = weeks.map((week, index) => ({
          label: `Week ${index + 1}`,
          from: week,
          to: endOfWeek(week, { weekStartsOn: 1 })
        }));
      }

      const results = await Promise.all(dateRanges.map(async ({ label, from, to }) => {
        const fromStr = format(from, 'yyyy-MM-dd');
        const toStr = format(to, 'yyyy-MM-dd');

        const { data: dehackingResult, error } = await supabase
          .from('dehacking_entries')
          .select(`
            pallet_count,
            management_brick_types!inner(name, category, grade, bricks_per_pallet)
          `)
          .gte('date', fromStr)
          .lte('date', toStr);

        if (error) {
          console.error('Error fetching dehacking data:', error);
          return {
            period: label,
            nfxImperial: 0,
            nfpImperial: 0,
            secondGradeImperial: 0,
            nfxMaxi: 0,
            nfpMaxi: 0,
            secondGradeMaxi: 0
          };
        }

        // Initialize counters
        let nfxImperial = 0;
        let nfpImperial = 0;
        let secondGradeImperial = 0;
        let nfxMaxi = 0;
        let nfpMaxi = 0;
        let secondGradeMaxi = 0;

        // Process dehacking data
        dehackingResult?.forEach((entry: any) => {
          const brickTypeName = entry.management_brick_types?.name || '';
          const brickCategory = entry.management_brick_types?.category || '';
          const grade = entry.management_brick_types?.grade || '';
          const brickCount = entry.pallet_count * (entry.management_brick_types?.bricks_per_pallet || 320);

          const nameAndCategoryLower = `${brickTypeName} ${brickCategory}`.toLowerCase();
          const gradeLower = grade.toLowerCase();

          if (nameAndCategoryLower.includes('imperial') || nameAndCategoryLower.includes('imp')) {
            if (gradeLower.includes('nfx')) {
              nfxImperial += brickCount;
            } else if (gradeLower.includes('nfp')) {
              nfpImperial += brickCount;
            } else if (gradeLower.includes('2nd') || gradeLower.includes('second')) {
              secondGradeImperial += brickCount;
            }
          } else if (nameAndCategoryLower.includes('maxi')) {
            if (gradeLower.includes('nfx')) {
              nfxMaxi += brickCount;
            } else if (gradeLower.includes('nfp')) {
              nfpMaxi += brickCount;
            } else if (gradeLower.includes('2nd') || gradeLower.includes('second')) {
              secondGradeMaxi += brickCount;
            }
          }
        });

        return {
          period: label,
          nfxImperial,
          nfpImperial,
          secondGradeImperial,
          nfxMaxi,
          nfpMaxi,
          secondGradeMaxi
        };
      }));

      return results;
    }
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Hammer className="h-5 w-5" />
            Dehacking Production
          </CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-40">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Hammer className="h-5 w-5" />
          Dehacking Production ({timeRange})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Period</TableHead>
              <TableHead className="text-right">NFX Imperial</TableHead>
              <TableHead className="text-right">NFP Imperial</TableHead>
              <TableHead className="text-right">2nd Grade Imperial</TableHead>
              <TableHead className="text-right">NFX Maxi</TableHead>
              <TableHead className="text-right">NFP Maxi</TableHead>
              <TableHead className="text-right">2nd Grade Maxi</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data?.map((row) => (
              <TableRow key={row.period}>
                <TableCell className="font-medium">{row.period}</TableCell>
                <TableCell className="text-right">{row.nfxImperial.toLocaleString()}</TableCell>
                <TableCell className="text-right">{row.nfpImperial.toLocaleString()}</TableCell>
                <TableCell className="text-right">{row.secondGradeImperial.toLocaleString()}</TableCell>
                <TableCell className="text-right">{row.nfxMaxi.toLocaleString()}</TableCell>
                <TableCell className="text-right">{row.nfpMaxi.toLocaleString()}</TableCell>
                <TableCell className="text-right">{row.secondGradeMaxi.toLocaleString()}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
