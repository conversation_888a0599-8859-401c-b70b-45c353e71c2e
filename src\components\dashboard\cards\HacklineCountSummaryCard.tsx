
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Calculator, Loader2, Package } from "lucide-react";
import { useHacklineCounts } from "@/hooks/useHacklineCounts";

export const HacklineCountSummaryCard = () => {
  const { data: hacklineCounts = [], isLoading } = useHacklineCounts();

  // Filter to only show current day's data
  const today = new Date().toISOString().split('T')[0]; // Get current date in YYYY-MM-DD format
  const todayCounts = hacklineCounts.filter(count => count.date.split('T')[0] === today);

  const totalPallets = todayCounts.reduce((sum, count) => sum + count.pallet_count, 0);
  const totalBricks = todayCounts.reduce((sum, count) => sum + count.count_total, 0);

  // Aggregate by brick type for today
  const brickTypeBreakdown = todayCounts.reduce((acc, count) => {
    const brickType = count.pallet_type;
    if (!acc[brickType]) {
      acc[brickType] = { pallets: 0, bricks: 0 };
    }
    acc[brickType].pallets += count.pallet_count;
    acc[brickType].bricks += count.count_total;
    return acc;
  }, {} as Record<string, { pallets: number; bricks: number }>);

  const typeBreakdown = Object.entries(brickTypeBreakdown).map(([type, data]) => ({
    type,
    pallets: data.pallets,
    bricks: data.bricks,
  })).sort((a, b) => b.pallets - a.pallets);

  // Get recent entries from today (last 3)
  const recentEntries = todayCounts.slice(0, 3);

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-slate-800 flex items-center gap-2">
          <Calculator size={20} />
          Hackline Count
        </CardTitle>
        <p className="text-sm text-slate-600">Today's pallets drying in the sun</p>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-slate-500" />
          </div>
        ) : todayCounts.length > 0 ? (
          <div className="space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-slate-800">{totalPallets.toLocaleString()}</p>
                <p className="text-xs text-slate-500">Total Pallets</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-slate-800">{totalBricks.toLocaleString()}</p>
                <p className="text-xs text-slate-500">Total Bricks</p>
              </div>
            </div>

            {/* Brick Type Breakdown */}
            {typeBreakdown.length > 0 && (
              <div className="border-t pt-3">
                <h4 className="text-sm font-medium text-slate-700 mb-2">By Brick Type</h4>
                <div className="space-y-2">
                  {typeBreakdown.map(item => (
                    <div key={item.type} className="flex justify-between items-center py-2 px-3 bg-slate-50 rounded-md">
                      <span className="text-slate-700 font-medium">{item.type}</span>
                      <div className="text-right">
                        <div className="font-semibold text-slate-800">{item.pallets.toLocaleString()} pallets</div>
                        <div className="text-xs text-slate-500">({item.bricks.toLocaleString()} bricks)</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recent Entries */}
            {recentEntries.length > 0 && (
              <div className="border-t pt-3">
                <h4 className="text-sm font-medium text-slate-700 mb-2">Recent Entries</h4>
                <div className="space-y-2">
                  {recentEntries.map(entry => (
                    <div key={entry.id} className="flex justify-between items-center text-sm">
                      <span className="text-slate-600">
                        {new Date(entry.date).toLocaleDateString()}
                      </span>
                      <div className="flex items-center gap-2">
                        <Package size={12} className="text-slate-400" />
                        <span className="font-medium text-slate-700">
                          {entry.pallet_count} {entry.pallet_type}
                        </span>
                        <span className="text-slate-500">
                          ({entry.count_total.toLocaleString()} bricks)
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <Calculator className="h-12 w-12 text-slate-300 mx-auto mb-3" />
            <p className="text-slate-500 font-medium">No hackline counts</p>
            <p className="text-sm text-slate-400">available</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
