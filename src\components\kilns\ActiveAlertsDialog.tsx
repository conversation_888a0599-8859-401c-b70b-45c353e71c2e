
import React from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface AlertItem {
  id: string;
  kiln: string;
  chamber: string;
  parameter: string;
  value: string;
  expected: string;
  type: 'temperature' | 'pressure' | 'other';
  severity: 'high' | 'medium' | 'low';
  timestamp: string;
}

interface ActiveAlertsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  alerts: AlertItem[];
}

export const ActiveAlertsDialog: React.FC<ActiveAlertsDialogProps> = ({
  open,
  onOpenChange,
  alerts
}) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAlertVariant = (severity: string) => {
    return severity === 'high' ? 'destructive' : 'default';
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            All Active Alerts ({alerts.length})
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {alerts.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No active alerts at this time.
            </div>
          ) : (
            alerts.map((alert) => (
              <Alert 
                key={alert.id} 
                variant={getAlertVariant(alert.severity)}
                className="border-l-4"
              >
                <div className="flex items-start justify-between">
                  <AlertDescription className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className={getSeverityColor(alert.severity)}>
                        {alert.severity.toUpperCase()}
                      </Badge>
                      <span className="text-xs text-gray-500">{alert.timestamp}</span>
                    </div>
                    <div className="space-y-1">
                      <div className="font-semibold text-red-800">
                        {alert.kiln} {alert.chamber}
                      </div>
                      <div className="text-red-700">
                        <strong>{alert.parameter}:</strong> {alert.value}
                      </div>
                      <div className="text-sm text-gray-600">
                        Expected range: {alert.expected}
                      </div>
                    </div>
                  </AlertDescription>
                </div>
              </Alert>
            ))
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
